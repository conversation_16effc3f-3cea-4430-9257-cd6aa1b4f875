package za.co.wethinkcode.robots.world;

import org.json.JSONObject;
import za.co.wethinkcode.robots.OperationalStatus;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.UpdateResponse;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.*;
import za.co.wethinkcode.robots.robot.Robot;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.logging.Logger;
import static za.co.wethinkcode.robots.config.Config.*;
import static za.co.wethinkcode.robots.UpdateResponse.*;

/**
 * The World class represents the game world where robots and obstacles exist.
 * It contains methods to manage robots, check positions, and update their states.
 */
public class World {
    private final Position TOP_LEFT;
    private final Position BOTTOM_RIGHT;
    private final Maze maze;
    private final List<Robot> robots;
    private Robot currentRobot;
    private final List<Obstacle> obstacleList;
    private WorldGUI gui;
    private final boolean GUI;
    private int MAX_ROBOTS;
    public int worldSize;
    private static final Logger LOGGER = Logger.getLogger(World.class.getName());

    public WorldGUI getGui() {
        return gui;
    }

    public void setGui(WorldGUI gui) {
        this.gui = gui;
    }

    public Position getTOP_LEFT() {
        return TOP_LEFT;
    }

    public Position getBOTTOM_RIGHT() {
        return BOTTOM_RIGHT;
    }

    public void setWorldSize(int size) {
        this.worldSize = size;
    }

    /**
     * Gets the maze object.
     * @return The maze object.
     */
    public Maze getMaze() {
        return maze;
    }

    /**
     * Constructor for the World class.
     * Initializes the maze and sets the boundaries of the world.
     */
    public World(boolean GUI) {
        this(GUI, "");
    }

    /**
     * Constructor for the World class with custom maze configuration.
     * Initializes the maze with the provided configuration and sets the boundaries of the world.
     * @param GUI Whether to enable GUI
     * @param mazeConfig The maze configuration string (empty string uses config file)
     */
    public World(boolean GUI, String mazeConfig) {
        this.maze = new Maze(mazeConfig);
        this.TOP_LEFT = new Position(0, 0);
        this.BOTTOM_RIGHT = new Position(HEIGHT - 1, WIDTH - 1);
        this.MAX_ROBOTS = HEIGHT * WIDTH;
        obstacleList = maze.getObstacles();
        this.GUI = GUI;
        robots = new ArrayList<>();
        if (GUI) gui = new WorldGUI(this);
    }

    /**
     * Sets the current robot by its name.
     * @param name The name of the robot to set as current.
     */
    public void setCurrentRobotByName(String name) {
        for (Robot robot : robots) {
            if (robot.getName().equals(name)) {
                currentRobot = robot;
                break;
            }
        }
    }

    /**
     * Sets the current robot directly.
     * @param robot The robot to set as current.
     */
    public void setCurrentRobot(Robot robot) {
        currentRobot = robot;
    }

    /**
     * Returns the list of obstacles in the maze.
     * @return A list of obstacles.
     */
    public List<Obstacle> getObstacles() {
        return maze.getObstacles();
    }

    /**
     * Returns the current robot.
     * @return The current robot.
     */
    public Robot getCurrentRobot() {
        return currentRobot;
    }

    /**
     * Returns the list of robots in the world.
     * @return A list of robots.
     */
    public List<Robot> getBots() {
        return robots;
    }

    /**
     * Adds a robot to the world.
     * @param robot The robot to add.
     */
    public void addRobot(Robot robot) {
        robots.add(robot);
        if (GUI) gui.update();
    }

    /**
     * Checks if a new position is allowed based on the current robot's position and obstacles.
     * @param newPosition The new position to check.
     * @return true if the new position is allowed, false otherwise.
     */
    public boolean isNewPositionAllowed(Position newPosition) {
        if (!newPosition.isIn(TOP_LEFT, BOTTOM_RIGHT)) return false;
        if (isOccupiedByOtherRobot(newPosition)) return false;
        if (isObstacleBlocking(newPosition)) return false;
        return true;
    }

    private boolean isOccupiedByOtherRobot(Position newPosition) {
        for (Robot robot : robots) {
            if (robot.getName().equals(currentRobot.getName())) continue;
            if (robot.getPosition().equals(newPosition)) return true;
        }
        return false;
    }

    private boolean isObstacleBlocking(Position newPosition) {
        for (Obstacle obstacle : obstacleList) {
            if ((obstacle.blocksPosition(newPosition) || obstacle.blocksPath(currentRobot.getPosition(), newPosition)) && obstacle.getType() != ObstacleType.BOTTOMLESS_PIT)
                return true;
        }
        return false;
    }

    /**
     * Checks if a new position is allowed for launching based on the current robot's positions and obstacles.
     * @param newPosition The new position to check.
     * @return true if the new position is allowed, false otherwise.
     */
    public boolean isLaunchAllowed(Position newPosition) {
        if (isWorldFull()) {
            return false;
        }
        if (isPositionOccupiedByRobot(newPosition)) {
            return false;
        }
        if (isObstacleBlockingByObstacle(newPosition)) {
            return false;
        }
        return true;
    }

    private boolean isWorldFull() {
        return robots.size() >= MAX_ROBOTS;
    }

    private boolean isPositionOccupiedByRobot(Position newPosition) {
        for (Robot robot : robots) {
            if (robot.getPosition().equals(newPosition)) return true;
        }
        return false;
    }

    private boolean isObstacleBlockingByObstacle(Position newPosition) {
        for (Obstacle obstacle : obstacleList) {
            if (obstacle.blocksPosition(newPosition)) return true;
        }
        return false;
    }

    /**
     * checks if robot can move from current position to the new without being obstructed
     * @param newPos - new position that the bot is attempting to move to
     * @return boolean that states if bot can move or not.
     */
    public boolean isMovementObstructed(Position newPos) {
        for (Obstacle o : obstacleList) {
            if (o.getType() == ObstacleType.MOUNTAIN) {
                if (o.blocksPosition(newPos)) {
                    return true; // Blocked by mountain
                }
            }
        }
        return false; // Not obstructed
    }

    /**
     * Updates the position of the current robot based on the number of steps.
     * @param nrSteps The number of steps to move.
     * @return An UpdateResponse indicating the result of the update.
     */
    public UpdateResponse updatePosition(int nrSteps) {
        Position oldPos = getCurrentRobot().getPosition();
        Position pos = oldPos.newPos(currentRobot.getCurrentDirection(), nrSteps);
        for (Obstacle o : getObstacles()) {
            if (isBlockedByBottomlessPit(oldPos, pos, o)) {
                getCurrentRobot().setPosition(pos);
                getCurrentRobot().setStatus(OperationalStatus.DEAD);
                if (GUI) gui.update();
                return UpdateResponse.DIED_FELL_IN_PIT;
            }
        }
        if (isPositionValid(oldPos, pos)) {
            getCurrentRobot().setPosition(pos);
            if (GUI) gui.update();
            return SUCCESS;
        } else if (!pos.isIn(TOP_LEFT, BOTTOM_RIGHT)) {
            getCurrentRobot().setPosition(pos);
            getCurrentRobot().setStatus(OperationalStatus.DEAD);
            if (GUI) gui.update();
            return FAILURE_OUT_OF_BOUNDS;
        }
        if (GUI) gui.update();
        return FAILURE_OBSTRUCTED;
    }

    private boolean isPositionValid(Position oldPos, Position pos) {
        return isInValidAreaAndAllowed(pos);
    }

    private boolean isInValidAreaAndAllowed(Position pos) {
        return pos.isIn(TOP_LEFT, BOTTOM_RIGHT) && isNewPositionAllowed(pos);
    }

    private boolean isBlockedByBottomlessPit(Position oldPos, Position pos, Obstacle obstacle) {
        return obstacle.getType() == ObstacleType.BOTTOMLESS_PIT && (obstacle.blocksPosition(pos) || obstacle.blocksPath(oldPos, pos));
    }

    /**
     * deletes robots with a DEAD status
     */
    public void deleteDeadBots() {
        robots.removeIf(r -> r.getStatus() == OperationalStatus.DEAD);
        if (GUI) gui.update();
    }

    /**
     * Returns the maze object.
     * @return The maze object.
     */
    public List<Robot> getRobots() {
        return robots;
    }

    private JSONObject createResponse(String result, JSONObject data, String message) {
        return new JSONObject()
                .put("result", result)
                .put("data", data)
                .put("message", message);
    }

    private String getEdgeMessage(Position newPosition) {
        if (newPosition.getY() > TOP_LEFT.getY()) return "At the NORTH edge";
        if (newPosition.getY() < BOTTOM_RIGHT.getY()) return "At the SOUTH edge";
        if (newPosition.getX() > BOTTOM_RIGHT.getX()) return "At the EAST edge";
        if (newPosition.getX() < TOP_LEFT.getX()) return "At the WEST edge";
        return null;
    }

    private Position calculateNewPosition(Position current, int steps, String direction) {
        int x = current.getX();
        int y = current.getY();
        return switch (direction) {
            case "NORTH" -> new Position(x, y + steps);
            case "SOUTH" -> new Position(x, y - steps);
            case "EAST" -> new Position(x + steps, y);
            case "WEST" -> new Position(x - steps, y);
            default -> current; // Fallback, no movement
        };
    }

    private String handleForwardCommand(JSONObject command) {
        if (!command.has("steps")) {
            return createResponse("ERROR", new JSONObject(), "Missing steps").toString();
        }
        int steps;
        try {
            steps = command.getInt("steps");
            if (steps <= 0) {
                return createResponse("ERROR", new JSONObject(), "Invalid number of steps").toString();
            }
        } catch (Exception e) {
            return createResponse("ERROR", new JSONObject(), "Invalid steps format").toString();
        }
        Position currentPosition = getCurrentRobot().getPosition();
        Position newPosition = calculateNewPosition(currentPosition, steps, getCurrentRobot().getCurrentDirection().toString());
        JSONObject data = new JSONObject()
                .put("x", currentPosition.getX())
                .put("y", currentPosition.getY());
        if (!isNewPositionAllowed(newPosition)) {
            String edgeMessage = getEdgeMessage(newPosition);
            return createResponse("OK", data, edgeMessage != null ? edgeMessage : "Blocked by obstacle or robot").toString();
        }
        getCurrentRobot().setPosition(newPosition);
        data.put("x", newPosition.getX()).put("y", newPosition.getY());
        return createResponse("OK", data, "Moved forward " + steps + " steps").toString();
    }

    private String handleLaunchCommand(JSONObject command) {
        if (!command.has("arguments")) {
            return createResponse("ERROR", new JSONObject(), "Missing arguments").toString();
        }
        try {
            org.json.JSONArray arguments = command.getJSONArray("arguments");
            if (arguments.length() < 1) {
                return createResponse("ERROR", new JSONObject(), "Invalid arguments format").toString();
            }
            String robotType = arguments.getString(0);
            String robotName = command.getString("robot");

            // Check if robot name already exists
            for (Robot robot : robots) {
                if (robot.getName().equals(robotName)) {
                    return createResponse("ERROR", new JSONObject(), "Robot name already exists").toString();
                }
            }



            // Find a random valid position
            List<Position> validPositions = new ArrayList<>();
            for (int x = TOP_LEFT.getX(); x <= BOTTOM_RIGHT.getX(); x++) {
                for (int y = TOP_LEFT.getY(); y <= BOTTOM_RIGHT.getY(); y++) {
                    Position pos = new Position(x, y);
                    if (isLaunchAllowed(pos)) {
                        validPositions.add(pos);
                    }
                }
            }



            if (validPositions.isEmpty()) {
                return createResponse("ERROR", new JSONObject(), "No space available in world").toString();
            }

            // Select a random position
            Random random = new Random();
            Position newPosition = validPositions.get(random.nextInt(validPositions.size()));

            // Create and add the robot
            Robot newRobot = new Robot(robotName, robotType);
            newRobot.setPosition(newPosition);
            addRobot(newRobot);
            setCurrentRobot(newRobot);

            JSONObject data = new JSONObject()
                    .put("x", newPosition.getX())
                    .put("y", newPosition.getY());
            return createResponse("OK", data, "Robot '" + robotName + "' launched successfully").toString();
        } catch (Exception e) {
            return createResponse("ERROR", new JSONObject(), "Invalid launch command format").toString();
        }
    }

    public String handleCommand(String commandJson) {
        if (commandJson == null || commandJson.trim().isEmpty()) {
            return createResponse("ERROR", new JSONObject(), "Empty command").toString();
        }
        try {
            JSONObject command = new JSONObject(commandJson);
            if (!command.has("command") || !command.has("robot")) {
                return createResponse("ERROR", new JSONObject(), "Missing command or robot name").toString();
            }
            String commandType = command.getString("command");
            String robotName = command.getString("robot");
            if (commandType.equals("launch")) {
                return handleLaunchCommand(command); // Handle launch command
            }
            if (currentRobot == null || !currentRobot.getName().equals(robotName)) {
                return createResponse("ERROR", new JSONObject(), "Robot not found or not current").toString();
            }
            if (commandType.equals("forward")) {
                return handleForwardCommand(command);
            }
            return createResponse("ERROR", new JSONObject(), "Unsupported command: " + commandType).toString();
        } catch (Exception e) {
            return createResponse("ERROR", new JSONObject(), "Invalid command format").toString();
        }
    }
}